<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北京移动资费客户满意度座谈会定性报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
            font-weight: 300;
        }

        .header .subtitle {
            font-size: 1em;
            opacity: 0.9;
        }

        .content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 15px;
            padding: 20px;
            position: relative;
        }

        .corner-section {
            background: #f8f9ff;
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #e0e6ff;
            transition: all 0.3s ease;
        }

        .corner-section.problem {
            border-color: #ffebee;
            background: #ffebee;
        }

        .corner-section.solution {
            border-color: #e8f5e8;
            background: #e8f5e8;
        }

        .corner-title {
            font-size: 1em;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
        }

        .corner-section.problem .corner-title {
            color: #e74c3c;
        }

        .corner-section.solution .corner-title {
            color: #27ae60;
        }

        .corner-list {
            list-style: none;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .corner-list li {
            margin-bottom: 6px;
            position: relative;
            padding-left: 15px;
        }

        .corner-section.problem .corner-list li::before {
            content: '⚠️';
            position: absolute;
            left: 0;
            font-size: 0.8em;
        }

        .corner-section.solution .corner-list li::before {
            content: '💡';
            position: absolute;
            left: 0;
            font-size: 0.8em;
        }

        .center-control {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            width: 200px;
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            z-index: 10;
        }

        .center-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }

        .nav-buttons {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.85em;
            transition: all 0.3s ease;
            text-align: center;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .nav-btn.active {
            background: rgba(255, 255, 255, 0.4);
            border-color: rgba(255, 255, 255, 0.6);
        }

        .hidden {
            display: none;
        }

        .footer {
            background: #f8f9fa;
            padding: 10px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
            flex-shrink: 0;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>北京移动资费客户满意度座谈会定性报告</h1>
            <div class="subtitle">用户对资费的联想包括合理定价和自主、明白、明智消费四个非竞争因素</div>
        </div>

        <div class="content">
            <!-- 左上角：当前需求的问题1 -->
            <div class="corner-section problem" id="problem1">
                <h4 class="corner-title">存在问题 (1/2)</h4>
                <ul class="corner-list" id="problem1-list">
                    <!-- 动态内容 -->
                </ul>
            </div>

            <!-- 右上角：当前需求的问题2 -->
            <div class="corner-section problem" id="problem2">
                <h4 class="corner-title">存在问题 (2/2)</h4>
                <ul class="corner-list" id="problem2-list">
                    <!-- 动态内容 -->
                </ul>
            </div>

            <!-- 左下角：当前需求的创新点1 -->
            <div class="corner-section solution" id="solution1">
                <h4 class="corner-title">运营创新优化点 (1/2)</h4>
                <ul class="corner-list" id="solution1-list">
                    <!-- 动态内容 -->
                </ul>
            </div>

            <!-- 右下角：当前需求的创新点2 -->
            <div class="corner-section solution" id="solution2">
                <h4 class="corner-title">运营创新优化点 (2/2)</h4>
                <ul class="corner-list" id="solution2-list">
                    <!-- 动态内容 -->
                </ul>
            </div>

            <!-- 中央控制区域 -->
            <div class="center-control">
                <div class="center-title">资费需求分析</div>
                <div class="nav-buttons">
                    <button class="nav-btn active" onclick="showContent('reasonable')">合理定价</button>
                    <button class="nav-btn" onclick="showContent('autonomous')">自主消费</button>
                    <button class="nav-btn" onclick="showContent('clear')">明白消费</button>
                    <button class="nav-btn" onclick="showContent('smart')">明智消费</button>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 北京移动资费客户满意度调研报告 | 数据来源：客户座谈会定性分析</p>
        </div>
    </div>

    <script>
        // 数据定义
        const contentData = {
            reasonable: {
                problems: [
                    "套餐价格与实际价值不匹配，用户感觉性价比低",
                    "同类产品价格差异大，缺乏统一标准",
                    "价格调整频繁，用户难以适应"
                ],
                solutions: [
                    "建立基于用户价值的定价模型",
                    "推出阶梯式定价，满足不同消费层次",
                    "定期进行市场价格调研，保持竞争力"
                ]
            },
            autonomous: {
                problems: [
                    "套餐选择权有限，无法根据个人需求定制",
                    "强制捆绑服务，用户缺乏选择自由",
                    "变更套餐流程复杂，限制用户自主权"
                ],
                solutions: [
                    "推出模块化套餐，用户可自由组合",
                    "简化套餐变更流程，支持在线自助操作",
                    "提供个性化推荐，但保留用户最终选择权"
                ]
            },
            clear: {
                problems: [
                    "套餐条款复杂，用户难以理解",
                    "账单信息不透明，隐性收费较多",
                    "消费提醒不及时，用户容易超支"
                ],
                solutions: [
                    "简化套餐描述，使用通俗易懂的语言",
                    "提供详细透明的账单解读服务",
                    "建立实时消费提醒机制"
                ]
            },
            smart: {
                problems: [
                    "缺乏消费指导，用户容易过度消费",
                    "没有消费分析工具，用户无法优化支出",
                    "优惠信息传达不及时，用户错失省钱机会"
                ],
                solutions: [
                    "提供智能消费建议和分析报告",
                    "开发消费优化工具，帮助用户节省费用",
                    "主动推送个性化优惠信息"
                ]
            }
        };

        function showContent(type) {
            // 更新按钮状态
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 获取数据
            const data = contentData[type];

            // 分配问题到两个角落
            const problems1 = data.problems.slice(0, Math.ceil(data.problems.length / 2));
            const problems2 = data.problems.slice(Math.ceil(data.problems.length / 2));

            // 分配解决方案到两个角落
            const solutions1 = data.solutions.slice(0, Math.ceil(data.solutions.length / 2));
            const solutions2 = data.solutions.slice(Math.ceil(data.solutions.length / 2));

            // 更新内容
            updateCornerContent('problem1-list', problems1);
            updateCornerContent('problem2-list', problems2);
            updateCornerContent('solution1-list', solutions1);
            updateCornerContent('solution2-list', solutions2);
        }

        function updateCornerContent(elementId, items) {
            const element = document.getElementById(elementId);
            element.innerHTML = '';
            items.forEach(item => {
                const li = document.createElement('li');
                li.textContent = item;
                element.appendChild(li);
            });
        }

        // 页面加载时默认显示第一个
        window.onload = function() {
            showContent('reasonable');
        }
    </script>
</body>
</html>
