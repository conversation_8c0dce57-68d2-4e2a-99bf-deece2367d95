<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北京移动资费客户满意度座谈会定性报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            color: #e8eaed;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #f8fafc;
            padding: 25px;
            text-align: center;
            flex-shrink: 0;
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 8px;
            font-weight: 300;
            letter-spacing: 2px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.85;
            font-weight: 300;
        }

        .content {
            flex: 1;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
            padding: 30px;
            overflow: hidden;
        }

        /* 左侧金字塔区域 */
        .pyramid-section {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(148, 163, 184, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .pyramid-title {
            font-size: 1.3em;
            color: #f8fafc;
            margin-bottom: 25px;
            text-align: center;
            font-weight: 300;
            letter-spacing: 1px;
        }

        .pyramid {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
        }

        .pyramid-level {
            background: linear-gradient(135deg, #475569 0%, #64748b 100%);
            color: #f8fafc;
            padding: 15px 25px;
            border-radius: 8px;
            font-weight: 400;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(148, 163, 184, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .pyramid-level:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .pyramid-level.active {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .pyramid-level.level-1 { width: 100px; }
        .pyramid-level.level-2 { width: 140px; }
        .pyramid-level.level-3 { width: 180px; }
        .pyramid-level.level-4 { width: 220px; }

        /* 右侧详情区域 */
        .details-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 20px;
            overflow: hidden;
        }

        .demand-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(148, 163, 184, 0.2);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            position: relative;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .demand-card::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 20px;
            width: 3px;
            height: 40px;
            border-radius: 2px;
        }

        .demand-card.level-1::before { background: #3b82f6; }
        .demand-card.level-2::before { background: #06b6d4; }
        .demand-card.level-3::before { background: #10b981; }
        .demand-card.level-4::before { background: #f59e0b; }

        .demand-title {
            font-size: 14px;
            color: #f8fafc;
            margin-bottom: 10px;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-align: center;
            padding-bottom: 6px;
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
        }

        .content-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .problem-section, .solution-section {
            background: rgba(15, 23, 42, 0.3);
            border-radius: 6px;
            padding: 10px;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .section-title {
            font-size: 0.8em;
            margin-bottom: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            letter-spacing: 0.3px;
        }

        .problem-section .section-title {
            color: #fca5a5;
        }

        .solution-section .section-title {
            color: #86efac;
        }

        .section-title::before {
            margin-right: 8px;
            font-size: 1em;
        }

        .problem-section .section-title::before {
            content: '⚠';
        }

        .solution-section .section-title::before {
            content: '💡';
        }

        .content-list {
            list-style: none;
            font-size: 0.75em;
            line-height: 1.3;
        }

        .content-list li {
            margin-bottom: 5px;
            position: relative;
            padding-left: 12px;
            color: #cbd5e1;
        }

        .problem-section .content-list li::before {
            content: '•';
            color: #fca5a5;
            position: absolute;
            left: 0;
            font-weight: bold;
            font-size: 1.1em;
        }

        .solution-section .content-list li::before {
            content: '✓';
            color: #86efac;
            position: absolute;
            left: 0;
            font-weight: bold;
            font-size: 1em;
        }

        .hidden {
            display: none;
        }

        .footer {
            background: rgba(15, 23, 42, 0.8);
            padding: 15px;
            text-align: center;
            color: #94a3b8;
            border-top: 1px solid rgba(148, 163, 184, 0.2);
            flex-shrink: 0;
            font-size: 0.85em;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>北京移动资费客户满意度座谈会定性报告</h1>
            <div class="subtitle">用户对资费的联想包括合理定价和自主、明白、明智消费四个非竞争因素</div>
        </div>

        <div class="content">
            <!-- 左侧金字塔导航 -->
            <div class="pyramid-section">
                <div class="pyramid-title">资费需求分析</div>
                <div class="pyramid">
                    <div class="pyramid-level level-1">合理定价</div>
                    <div class="pyramid-level level-2">自主消费</div>
                    <div class="pyramid-level level-3">明白消费</div>
                    <div class="pyramid-level level-4">明智消费</div>
                </div>
            </div>

            <!-- 右侧详情区域 -->
            <div class="details-section">
                <!-- 合理定价 -->
                <div class="demand-card level-1">
                    <div class="demand-title">合理定价</div>
                    <div class="content-row">
                        <div class="problem-section">
                            <div class="section-title">存在问题</div>
                            <ul class="content-list" id="reasonable-problems">
                                <!-- 动态内容 -->
                            </ul>
                        </div>
                        <div class="solution-section">
                            <div class="section-title">运营创新优化点</div>
                            <ul class="content-list" id="reasonable-solutions">
                                <!-- 动态内容 -->
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 自主消费 -->
                <div class="demand-card level-2">
                    <div class="demand-title">自主消费</div>
                    <div class="content-row">
                        <div class="problem-section">
                            <div class="section-title">存在问题</div>
                            <ul class="content-list" id="autonomous-problems">
                                <!-- 动态内容 -->
                            </ul>
                        </div>
                        <div class="solution-section">
                            <div class="section-title">运营创新优化点</div>
                            <ul class="content-list" id="autonomous-solutions">
                                <!-- 动态内容 -->
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 明白消费 -->
                <div class="demand-card level-3">
                    <div class="demand-title">明白消费</div>
                    <div class="content-row">
                        <div class="problem-section">
                            <div class="section-title">存在问题</div>
                            <ul class="content-list" id="clear-problems">
                                <!-- 动态内容 -->
                            </ul>
                        </div>
                        <div class="solution-section">
                            <div class="section-title">运营创新优化点</div>
                            <ul class="content-list" id="clear-solutions">
                                <!-- 动态内容 -->
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 明智消费 -->
                <div class="demand-card level-4">
                    <div class="demand-title">明智消费</div>
                    <div class="content-row">
                        <div class="problem-section">
                            <div class="section-title">存在问题</div>
                            <ul class="content-list" id="smart-problems">
                                <!-- 动态内容 -->
                            </ul>
                        </div>
                        <div class="solution-section">
                            <div class="section-title">运营创新优化点</div>
                            <ul class="content-list" id="smart-solutions">
                                <!-- 动态内容 -->
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 北京移动资费客户满意度调研报告 | 数据来源：客户座谈会定性分析</p>
        </div>
    </div>

    <script>
        // 数据定义
        const contentData = {
            reasonable: {
                problems: [
                    "套餐价格与实际价值不匹配，用户感觉性价比低",
                    "同类产品价格差异大，缺乏统一标准",
                    "价格调整频繁，用户难以适应"
                ],
                solutions: [
                    "建立基于用户价值的定价模型",
                    "推出阶梯式定价，满足不同消费层次",
                    "定期进行市场价格调研，保持竞争力"
                ]
            },
            autonomous: {
                problems: [
                    "套餐选择权有限，无法根据个人需求定制",
                    "强制捆绑服务，用户缺乏选择自由",
                    "变更套餐流程复杂，限制用户自主权"
                ],
                solutions: [
                    "推出模块化套餐，用户可自由组合",
                    "简化套餐变更流程，支持在线自助操作",
                    "提供个性化推荐，但保留用户最终选择权"
                ]
            },
            clear: {
                problems: [
                    "套餐条款复杂，用户难以理解",
                    "账单信息不透明，隐性收费较多",
                    "消费提醒不及时，用户容易超支"
                ],
                solutions: [
                    "简化套餐描述，使用通俗易懂的语言",
                    "提供详细透明的账单解读服务",
                    "建立实时消费提醒机制"
                ]
            },
            smart: {
                problems: [
                    "缺乏消费指导，用户容易过度消费",
                    "没有消费分析工具，用户无法优化支出",
                    "优惠信息传达不及时，用户错失省钱机会"
                ],
                solutions: [
                    "提供智能消费建议和分析报告",
                    "开发消费优化工具，帮助用户节省费用",
                    "主动推送个性化优惠信息"
                ]
            }
        };

        function updateContent(elementId, items) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = '';
                items.forEach(item => {
                    const li = document.createElement('li');
                    li.textContent = item;
                    element.appendChild(li);
                });
            }
        }

        // 页面加载时显示所有内容
        window.onload = function() {
            // 加载所有数据
            Object.keys(contentData).forEach(type => {
                const data = contentData[type];
                updateContent(type + '-problems', data.problems);
                updateContent(type + '-solutions', data.solutions);
            });
        }
    </script>
</body>
</html>
